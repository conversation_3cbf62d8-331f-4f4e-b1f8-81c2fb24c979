import type { RoleType } from '@constants/role-type';
import type { CanActivate, ExecutionContext } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
// import type { UserAccountEntity } from '@modules/user/user-account.entity.ts';

@Injectable()
export class RolesGuard implements CanActivate {
	constructor(private readonly reflector: Reflector) {}

	canActivate(context: ExecutionContext): boolean {
		const roles = this.reflector.get<RoleType[] | undefined>(
			'roles',
			context.getHandler(),
		);

		if (!roles?.length) {
			return true;
		}

		// const request = context.switchToHttp().getRequest<{ user: UserAccountEntity }>();
		// const user = request.user;

		// return roles.includes(user.role);
		return true;
	}
}
