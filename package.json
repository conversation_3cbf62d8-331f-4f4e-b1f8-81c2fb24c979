{"name": "fs_player_service", "version": "1.0.0", "description": "FunStudio Player Service", "author": "FGP <<EMAIL>>", "private": true, "license": "MIT", "type": "module", "scripts": {"build:prod": "nest build", "start:dev": "vite", "nest:start": "nest start ./src/main.ts", "nest:start:dev": "nest start --watch", "nest:start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js", "start:dev:bun": "bun run ./src/main.ts", "watch:bun": "bun run --watch ./src/main.ts", "build:bun": "bun build --external=class-validator --external=@nestjs/sequelize --external=@mikro-orm/core --external=@nestjs/mongoose --external=mock-aws-s3 --external=hbs --external=aws-sdk --external=nock --external=@nestjs/websockets/socket-module --external=class-transformer --external=@nestjs/microservices --define process.env.NODE_ENV:\"'production'\" --target=bun --minify ./dist/main.js --outdir=dist --format esm ./src/main.ts", "typeorm": "typeorm-ts-node-esm", "migration:generate": "yarn run typeorm migration:generate -d ormconfig.ts", "migration:create": "yarn run typeorm migration:create -d ormconfig.ts", "generate": "nest g -c awesome-nestjs-schematics --no-spec", "g": "yarn generate", "migration:revert": "yarn run typeorm migration:revert", "schema:drop": "yarn run typeorm schema:drop", "watch:dev": "nest start --watch ./src/main.ts", "lint": "eslint ./src/ --ext .ts", "lint:fix": "eslint ./src/ --ext .ts --fix", "prettier": "npx prettier --write ./src//*.ts", "eslint": "npx eslint --fix ./src//*.ts", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./test/jest-e2e.json", "docs:dev": "vuepress dev -p 7070", "docs:build": "DEPLOY_ENV=gh-pages vuepress build", "docs:deploy": "yarn docs:build && gh-pages -d .vuepress/dist", "prepare": "husky", "release": "release-it"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@infisical/sdk": "4.0.2", "@nestjs/common": "^11.0.5", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.10", "@nestjs/cqrs": "^11.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.10", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.10", "@nestjs/swagger": "^11.0.5", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "bcrypt": "^6.0.0", "class-transformer": "~0.5.1", "class-validator": "~0.14.1", "compression": "^1.8.0", "express": "^5.0.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.11.20", "lodash": "^4.17.21", "mime-types": "^3.0.0", "morgan": "^1.10.0", "nestjs-cls": "^5.4.0", "nestjs-i18n": "^10.5.0", "parse-duration": "^2.1.3", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "tslib": "2.8.1", "typeorm": "0.3.20", "typeorm-transactional": "~0.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.21.0", "@nestjs/cli": "^11.0.4", "@nestjs/testing": "^11.0.10", "@stylistic/eslint-plugin": "^4.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.1", "@swc/plugin-transform-imports": "^7.0.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.15", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/node": "^22.13.5", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.25.0", "@vuepress/bundler-vite": "2.0.0-rc.19", "@vuepress/theme-default": "2.0.0-rc.68", "awesome-nestjs-schematics": "^10.1.1", "cross-env": "^7.0.3", "esbuild": "^0.25.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-canonical": "^5.1.3", "eslint-plugin-github": "^6.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-import-helpers": "^2.0.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-no-secrets": "^2.2.1", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "gh-pages": "^6.3.0", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "release-it": "^19.0.2", "sass-embedded": "^1.89.0", "supertest": "^7.1.1", "taze": "^19.1.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-node": "^5.0.1", "vite-tsconfig-paths": "^5.1.4", "vue": "^3.5.14", "vuepress": "2.0.0-rc.19"}, "lint-staged": {"*.ts": ["eslint --fix", "git add"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}