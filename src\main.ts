import './boilerplate.polyfill';

import { InfisicalSDK } from '@infisical/sdk';
import {
	ClassSerializerInterceptor,
	HttpStatus,
	UnprocessableEntityException,
	ValidationPipe,
} from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import type { NestExpressApplication } from '@nestjs/platform-express';
import { ExpressAdapter } from '@nestjs/platform-express';
import compression from 'compression';
import helmet from 'helmet';
import morgan from 'morgan';
import { initializeTransactionalContext } from 'typeorm-transactional';

import { AppModule } from './app.module.ts';
import { HttpExceptionFilter } from './filters/bad-request.filter.ts';
import { QueryFailedFilter } from './filters/query-failed.filter.ts';
import { TranslationInterceptor } from './interceptors/translation-interceptor.service.ts';
import { setupSwagger } from './setup-swagger.ts';
import { ApiConfigService } from './shared/services/api-config.service.ts';
import { TranslationService } from './shared/services/translation.service.ts';
import { SharedModule } from './shared/shared.module.ts';

async function loadEnvFromInfisical() {
	if (process.env.USE_INFISICAL !== 'true') {
		return;
	}

	const clientId = process.env.CLIENT_ID;
	const clientSecret = process.env.CLIENT_SECRET;
	const projectId = process.env.PROJECT_ID;
	const environment = process.env.ENVIRONMENT;

	if (!clientId || !clientSecret || !projectId || !environment) {
		throw new Error('Missing Infisical credentials in environment variables');
	}

	const infisical = new InfisicalSDK({ siteUrl: 'https://eu.infisical.com' });
	await infisical.auth().universalAuth.login({ clientId, clientSecret });

	const secretNames = [
		'NODE_ENV',
		'PORT',
		'TRANSPORT_PORT',
		'JWT_EXPIRATION_TIME',
		'FALLBACK_LANGUAGE',
		'ENABLE_ORM_LOGS',
		'ENABLE_DOCUMENTATION',
		'API_VERSION',
		'JWT_PRIVATE_KEY',
		'JWT_PUBLIC_KEY',
		'JWT_EXPIRATION_TIME',
		'JWT_REFRESH_TOKEN_EXPIRATION_TIME',
		'DB_TYPE',
		'DB_HOST',
		'DB_PORT',
		'DB_USERNAME',
		'DB_PASSWORD',
		'DB_DATABASE',
		'AWS_S3_ACCESS_KEY_ID',
		'AWS_S3_SECRET_ACCESS_KEY',
		'AWS_S3_BUCKET_REGION',
		'AWS_S3_API_VERSION',
		'AWS_S3_BUCKET_NAME',
		'NATS_ENABLED',
		'NATS_HOST',
		'NATS_PORT',
		'REDIS_CACHE_ENABLED',
		'REDIS_HOST',
		'REDIS_PORT',
		'THROTTLER_TTL',
		'THROTTLER_LIMIT',
		'GOOGLE_CLIENT_ID',
		'GOOGLE_CLIENT_SECRET',
		'FACEBOOK_CLIENT_ID',
		'FACEBOOK_CLIENT_SECRET',
	];

	await Promise.all(
		secretNames.map(async (name) => {
			const secret = await infisical.secrets().getSecret({
				environment,
				projectId,
				secretPath: '/',
				secretName: name,
			});

			if (secret.secretValue) {
				process.env[name] = secret.secretValue;
			}
		}),
	);
}

export async function bootstrap(): Promise<NestExpressApplication> {
	initializeTransactionalContext();

	// Setup Infisical
	await loadEnvFromInfisical();

	const app = await NestFactory.create<NestExpressApplication>(
		AppModule,
		new ExpressAdapter(),
		{
			cors: {
				origin: process.env.CORS_ORIGINS?.split(',') || [
					'http://localhost:3000',
				],
				methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
				credentials: true,
			},
		},
	);
	app.enable('trust proxy'); // only if you're behind a reverse proxy (Heroku, Bluemix, AWS ELB, Nginx, etc)
	app.use(helmet());
	// app.setGlobalPrefix('/api'); use api as global prefix if you don't have subdomain
	app.use(compression());
	app.use(morgan('combined'));
	app.enableVersioning();

	const reflector = app.get(Reflector);

	app.useGlobalFilters(
		new HttpExceptionFilter(reflector),
		new QueryFailedFilter(reflector),
	);

	app.useGlobalInterceptors(
		new ClassSerializerInterceptor(reflector),
		new TranslationInterceptor(
			app.select(SharedModule).get(TranslationService),
		),
	);

	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
			transform: true,
			dismissDefaultMessages: true,
			forbidNonWhitelisted: true,
			exceptionFactory: (errors) => new UnprocessableEntityException(errors),
		}),
	);

	const configService = app.select(SharedModule).get(ApiConfigService);

	// only start nats if it is enabled
	if (configService.natsEnabled) {
		const natsConfig = configService.natsConfig;
		app.connectMicroservice({
			transport: Transport.NATS,
			options: {
				url: `nats://${natsConfig.host}:${natsConfig.port}`,
				queue: 'main_service',
			},
		});

		await app.startAllMicroservices();
	}

	if (configService.documentationEnabled) {
		setupSwagger(app);
	}

	// Starts listening for shutdown hooks
	if (!configService.isDevelopment) {
		app.enableShutdownHooks();
	}

	const port = configService.appConfig.port;

	if ((import.meta as any).env?.PROD) {
		await app.listen(port);
		console.info(`server running on ${await app.getUrl()}`);
	}

	return app;
}

export const viteNodeApp = await bootstrap();
