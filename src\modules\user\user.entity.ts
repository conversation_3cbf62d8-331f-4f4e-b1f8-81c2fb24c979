import { AbstractEntity } from '@common/abstract.entity';
import { RoleType } from '@constants/role-type';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	OneToMany,
	OneToOne,
	PrimaryGeneratedColumn,
	VirtualColumn,
} from 'typeorm';

import { PostEntity } from '../post/post.entity.ts';
import type { UserDtoOptions } from './dtos/user.dto.ts';
import { UserDto } from './dtos/user.dto.ts';
import { UserSettingsEntity } from './user-settings.entity.ts';

@Entity({ name: 'users' })
@UseDto(UserDto)
export class UserEntity extends AbstractEntity<UserDto, UserDtoOptions> {
	@PrimaryGeneratedColumn('uuid')
	id!: Uuid;

	@Column({ nullable: true, type: 'varchar' })
	firstName!: string | null;

	@Column({ nullable: true, type: 'varchar' })
	lastName!: string | null;

	@Column({ type: 'enum', enum: RoleType, default: RoleType.USER })
	role!: RoleType;

	@Column({ unique: true, nullable: true, type: 'varchar' })
	email!: string | null;

	@Column({ nullable: true, type: 'varchar' })
	password!: string | null;

	@Column({ nullable: true, type: 'varchar' })
	phone!: string | null;

	@Column({ nullable: true, type: 'varchar' })
	avatar!: string | null;

	@VirtualColumn({
		query: (alias) =>
			`SELECT CONCAT(${alias}.first_name, ' ', ${alias}.last_name)`,
	})
	fullName!: string;

	@OneToOne(() => UserSettingsEntity, (userSettings) => userSettings.user)
	settings?: UserSettingsEntity;

	@OneToMany(() => PostEntity, (postEntity) => postEntity.user)
	posts?: PostEntity[];
}
