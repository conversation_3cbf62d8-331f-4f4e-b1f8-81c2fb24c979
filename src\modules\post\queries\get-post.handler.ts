import type { <PERSON><PERSON><PERSON>yH<PERSON><PERSON> } from '@nestjs/cqrs';
import { QueryHandler } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { PostEntity } from '../post.entity.ts';
import { GetPostQuery } from './get-post.query.ts';

@QueryHandler(GetPostQuery)
export class GetPostHandler implements IQueryHandler<GetPostQuery> {
	constructor(
		@InjectRepository(PostEntity)
		private postRepository: Repository<PostEntity>,
	) {}

	async execute(query: GetPostQuery) {
		return this.postRepository.findBy({
			userId: query.userId as never,
		});
	}
}
