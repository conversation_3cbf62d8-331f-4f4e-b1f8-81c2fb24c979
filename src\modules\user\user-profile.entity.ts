import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	Index,
	JoinColumn,
	OneToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { UserProfileDto } from './dtos/user-profile.dto.ts';

@Index('user_profile_pkey', ['profileId'], { unique: true })
@Index('user_profile_user_id_key', ['userId'], { unique: true })
@Entity('user_profile', { schema: 'public' })
@UseDto(UserProfileDto)
export class UserProfileEntity extends AbstractEntity<UserProfileDto> {
	@PrimaryGeneratedColumn({ type: 'bigint', name: 'profile_id' })
	profileId!: number;

	@Column('bigint', { name: 'user_id', unique: true })
	userId!: number;

	@Column('character varying', {
		name: 'display_name',
		length: 100,
		nullable: true,
		default: '',
	})
	displayName?: string | null;

	@Column('boolean', {
		name: 'gender',
		nullable: true,
	})
	gender?: boolean | null;

	@Column('date', { name: 'dob', nullable: true })
	dob?: string | null;

	@Column('character varying', {
		name: 'avatar_url',
		nullable: true,
		length: 500,
	})
	avatarUrl?: string | null;

	@Column('character varying', { name: 'phone', nullable: true, length: 20 })
	phone?: string | null;

	@Column('character varying', { name: 'address', nullable: true, length: 500 })
	address?: string | null;

	@Column('timestamp without time zone', {
		name: 'created_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare createdAt: Date;

	@Column('timestamp without time zone', {
		name: 'updated_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare updatedAt: Date;

	@OneToOne('UserAccountEntity', 'userProfile', { onDelete: 'CASCADE' })
	@JoinColumn([{ name: 'user_id', referencedColumnName: 'userId' }])
	declare user: 'UserAccountEntity';
}
