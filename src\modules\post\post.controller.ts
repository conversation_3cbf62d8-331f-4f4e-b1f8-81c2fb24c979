import type { PageDto } from '@common/dto/page.dto';
import { RoleType } from '@constants/role-type';
import { ApiPageResponse } from '@decorators/api-page-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth, UUIDParam } from '@decorators/http.decorators';
import { UserEntity } from '@modules/user/user.entity';
import {
	Body,
	Controller,
	Delete,
	Get,
	HttpCode,
	HttpStatus,
	Post,
	Put,
	Query,
} from '@nestjs/common';
import {
	ApiAcceptedResponse,
	ApiCreatedResponse,
	ApiOkResponse,
	ApiTags,
} from '@nestjs/swagger';

import { UseLanguageInterceptor } from '../../interceptors/language-interceptor.service.ts';
import { CreatePostDto } from './dtos/create-post.dto.ts';
import { PostDto } from './dtos/post.dto.ts';
import { PostPageOptionsDto } from './dtos/post-page-options.dto.ts';
import { UpdatePostDto } from './dtos/update-post.dto.ts';
import { PostService } from './post.service.ts';

@Controller('posts')
@ApiTags('posts')
export class PostController {
	constructor(private postService: PostService) {}

	@Post()
	@Auth([RoleType.USER])
	@HttpCode(HttpStatus.CREATED)
	@ApiCreatedResponse({ type: PostDto })
	async createPost(
		@Body() createPostDto: CreatePostDto,
		@AuthUser() user: UserEntity,
	) {
		const postEntity = await this.postService.createPost(
			user.id,
			createPostDto,
		);

		return postEntity.toDto();
	}

	@Get()
	@Auth([RoleType.USER])
	@UseLanguageInterceptor()
	@ApiPageResponse({ type: PostDto })
	async getPosts(
		@Query() postsPageOptionsDto: PostPageOptionsDto,
	): Promise<PageDto<PostDto>> {
		return this.postService.getAllPost(postsPageOptionsDto);
	}

	@Get(':id')
	@Auth([])
	@HttpCode(HttpStatus.OK)
	@ApiOkResponse({ type: PostDto })
	async getSinglePost(@UUIDParam('id') id: Uuid): Promise<PostDto> {
		const entity = await this.postService.getSinglePost(id);

		return entity.toDto();
	}

	@Put(':id')
	@HttpCode(HttpStatus.ACCEPTED)
	@ApiAcceptedResponse()
	updatePost(
		@UUIDParam('id') id: Uuid,
		@Body() updatePostDto: UpdatePostDto,
	): Promise<void> {
		return this.postService.updatePost(id, updatePostDto);
	}

	@Delete(':id')
	@HttpCode(HttpStatus.ACCEPTED)
	@ApiAcceptedResponse()
	async deletePost(@UUIDParam('id') id: Uuid): Promise<void> {
		await this.postService.deletePost(id);
	}
}
