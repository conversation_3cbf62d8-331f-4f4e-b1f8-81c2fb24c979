import { AbstractDto } from '@common/dto/abstract.dto';
import {
	BooleanFieldOptional,
	DateField,
	DateFieldOptional,
	NumberField,
	PhoneFieldOptional,
	StringFieldOptional,
} from '@decorators/field.decorators';

import type { UserProfileEntity } from '../user-profile.entity';

export class UserProfileDto extends AbstractDto {
	@NumberField()
	profileId!: number;

	@NumberField()
	userId!: number;

	@StringFieldOptional({ nullable: true })
	displayName?: string | null;

	@BooleanFieldOptional({ nullable: true })
	gender?: boolean | null;

	@StringFieldOptional({ nullable: true })
	avatarUrl?: string | null;

	@DateFieldOptional()
	dob?: Date | null;

	@PhoneFieldOptional({ nullable: true })
	phone?: string | null;

	@StringFieldOptional({ nullable: true })
	address?: string | null;

	@DateField()
	declare createdAt: Date;

	@DateField()
	declare updatedAt: Date;

	constructor(userProfile: UserProfileEntity) {
		super(userProfile);
		this.profileId = userProfile.profileId;
		this.userId = userProfile.userId;
		this.displayName = userProfile.displayName;
		this.gender = userProfile.gender;
		this.avatarUrl = userProfile.avatarUrl;
		this.dob = userProfile.dob ? new Date(userProfile.dob) : null;
		this.phone = userProfile.phone;
		this.address = userProfile.address;
		this.createdAt = userProfile.createdAt;
		this.updatedAt = userProfile.updatedAt;
	}
}
