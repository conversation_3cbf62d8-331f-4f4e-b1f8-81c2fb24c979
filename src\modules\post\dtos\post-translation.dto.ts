import { AbstractTranslationDto } from '@common/dto/abstract.dto';
import { LanguageCode } from '@constants/language-code';
import {
	EnumFieldOptional,
	StringFieldOptional,
} from '@decorators/field.decorators';

import type { PostTranslationEntity } from '../post-translation.entity.ts';

export class PostTranslationDto extends AbstractTranslationDto {
	@StringFieldOptional()
	title?: string;

	@StringFieldOptional()
	description?: string;

	@EnumFieldOptional(() => LanguageCode)
	languageCode?: LanguageCode;

	constructor(postTranslationEntity: PostTranslationEntity) {
		super(postTranslationEntity);
		this.title = postTranslationEntity.title;
		this.description = postTranslationEntity.description;
		this.languageCode = postTranslationEntity.languageCode;
	}
}
