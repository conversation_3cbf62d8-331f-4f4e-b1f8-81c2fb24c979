/* eslint-disable */
export default async () => {
    const t = {
        ["./constants/user"]: await import("./constants/user"),
        ["./modules/audit-log/audit-log.entity"]: await import("./modules/audit-log/audit-log.entity"),
        ["./modules/payment/payment-transaction.entity"]: await import("./modules/payment/payment-transaction.entity"),
        ["./modules/user/user-profile.entity"]: await import("./modules/user/user-profile.entity"),
        ["./modules/user/user-game-mapping.entity"]: await import("./modules/user/user-game-mapping.entity"),
        ["./common/dto/abstract.dto"]: await import("./common/dto/abstract.dto"),
        ["./constants/language-code"]: await import("./constants/language-code"),
        ["./constants/payment"]: await import("./constants/payment"),
        ["./constants/order"]: await import("./constants/order"),
        ["./common/dto/page-meta.dto"]: await import("./common/dto/page-meta.dto"),
        ["./constants/platform"]: await import("./constants/platform"),
        ["./modules/admin/dtos/admin-account.dto"]: await import("./modules/admin/dtos/admin-account.dto"),
        ["./modules/user/dtos/user-account.dto"]: await import("./modules/user/dtos/user-account.dto"),
        ["./modules/auth/dto/token-payload.dto"]: await import("./modules/auth/dto/token-payload.dto"),
        ["./constants/role-type"]: await import("./constants/role-type"),
        ["./modules/user/user.entity"]: await import("./modules/user/user.entity"),
        ["./modules/user/user-settings.entity"]: await import("./modules/user/user-settings.entity"),
        ["./modules/post/post.entity"]: await import("./modules/post/post.entity"),
        ["./modules/post/dtos/post-translation.dto"]: await import("./modules/post/dtos/post-translation.dto"),
        ["./modules/post/post-translation.entity"]: await import("./modules/post/post-translation.entity"),
        ["./common/dto/create-translation.dto"]: await import("./common/dto/create-translation.dto"),
        ["./modules/user/dtos/user-quickplay-response.dto"]: await import("./modules/user/dtos/user-quickplay-response.dto"),
        ["./modules/auth/dto/social-info.dto"]: await import("./modules/auth/dto/social-info.dto"),
        ["./modules/auth/dto/login-payload.dto"]: await import("./modules/auth/dto/login-payload.dto"),
        ["./modules/post/dtos/post.dto"]: await import("./modules/post/dtos/post.dto")
    };
    return { "@nestjs/swagger/plugin": { "models": [[import("./modules/user/dtos/user-account.dto"), { "UserAccountDto": { userId: { required: true, type: () => Number }, username: { required: true, type: () => String, nullable: true }, email: { required: false, type: () => String, nullable: true }, passwordHash: { required: false, type: () => String, nullable: true }, refreshToken: { required: false, type: () => String, nullable: true }, refreshTokenExpiresAt: { required: false, type: () => Date, nullable: true }, status: { required: true, enum: t["./constants/user"].UserAccountStatus }, accountType: { required: true, enum: t["./constants/user"].UserAccountType }, socialUid: { required: false, type: () => String, nullable: true }, linkedAt: { required: false, type: () => Date, nullable: true }, socialAccessToken: { required: false, type: () => String, nullable: true }, socialRefreshToken: { required: false, type: () => String, nullable: true }, userBalance: { required: true, type: () => Number }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, lastLoginAt: { required: false, type: () => Date, nullable: true }, createdAtIp: { required: false, type: () => String, nullable: true }, lastLoginAtIp: { required: false, type: () => String, nullable: true } } }], [import("./modules/audit-log/dtos/audit-log.dto"), { "AuditLogDto": { logId: { required: true, type: () => Number }, userId: { required: false, type: () => Number, nullable: true }, adminId: { required: false, type: () => Number, nullable: true }, actionType: { required: true, type: () => String }, description: { required: true, type: () => String, nullable: true }, ipAddress: { required: true, type: () => String, nullable: true }, createdAt: { required: true, type: () => Date } } }], [import("./modules/audit-log/audit-log.entity"), { "AuditLogEntity": { logId: { required: true, type: () => Number }, userId: { required: false, type: () => Number, nullable: true }, adminId: { required: false, type: () => Number, nullable: true }, actionType: { required: true, type: () => String }, description: { required: true, type: () => String, nullable: true }, ipAddress: { required: true, type: () => String, nullable: true }, createdAt: { required: true, type: () => Date }, admin: { required: false, type: () => String }, user: { required: false, type: () => String } } }], [import("./modules/user/dtos/user-game-mapping.dto"), { "UserGameMappingDto": { ugmId: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, gameId: { required: true, type: () => Number }, gameBalance: { required: true, type: () => Number }, assignedAt: { required: true, type: () => Date }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./modules/user/user-game-mapping.entity"), { "UserGameMappingEntity": { ugmId: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, gameId: { required: true, type: () => Number }, gameBalance: { required: true, type: () => Number }, assignedAt: { required: true, type: () => Date }, user: { required: true, type: () => String }, game: { required: true, type: () => String } } }], [import("./modules/user/dtos/user-profile.dto"), { "UserProfileDto": { profileId: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, displayName: { required: false, type: () => String, nullable: true }, gender: { required: false, type: () => Boolean, nullable: true }, avatarUrl: { required: false, type: () => String, nullable: true }, dob: { required: false, type: () => Date, nullable: true }, phone: { required: false, type: () => String, nullable: true }, address: { required: false, type: () => String, nullable: true }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./modules/user/user-profile.entity"), { "UserProfileEntity": { profileId: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, displayName: { required: false, type: () => String, nullable: true }, gender: { required: false, type: () => Boolean, nullable: true }, dob: { required: false, type: () => String, nullable: true }, avatarUrl: { required: false, type: () => String, nullable: true }, phone: { required: false, type: () => String, nullable: true }, address: { required: false, type: () => String, nullable: true }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, user: { required: true, type: () => String } } }], [import("./modules/user/user-account.entity"), { "UserAccountEntity": { userId: { required: true, type: () => Number }, username: { required: false, type: () => String, nullable: true }, email: { required: false, type: () => String, nullable: true }, passwordHash: { required: false, type: () => String }, refreshToken: { required: false, type: () => String, nullable: true }, refreshTokenExpiresAt: { required: false, type: () => Date }, status: { required: true, type: () => String }, accountType: { required: true, type: () => String }, socialUid: { required: false, type: () => String, nullable: true }, linkedAt: { required: false, type: () => Date, nullable: true }, socialAccessToken: { required: false, type: () => String, nullable: true }, socialRefreshToken: { required: false, type: () => String, nullable: true }, userBalance: { required: true, type: () => Number }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, lastLoginAt: { required: false, type: () => Date, nullable: true }, createdAtIp: { required: true, type: () => String, nullable: true }, lastLoginAtIp: { required: false, type: () => String, nullable: true }, auditLogs: { required: true, type: () => [t["./modules/audit-log/audit-log.entity"].AuditLogEntity] }, paymentTransactions: { required: true, type: () => [t["./modules/payment/payment-transaction.entity"].PaymentTransactionEntity] }, userProfile: { required: true, type: () => t["./modules/user/user-profile.entity"].UserProfileEntity }, userGameMappings: { required: true, type: () => [t["./modules/user/user-game-mapping.entity"].UserGameMappingEntity] } } }], [import("./common/dto/abstract.dto"), { "AbstractDto": { createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, translations: { required: false, type: () => [t["./common/dto/abstract.dto"].AbstractTranslationDto] } }, "AbstractTranslationDto": {} }], [import("./common/abstract.entity"), { "AbstractEntity": { createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, translations: { required: false } }, "AbstractTranslationEntity": { languageCode: { required: true, enum: t["./constants/language-code"].LanguageCode } } }], [import("./modules/payment/dtos/payment-transaction.dto"), { "PaymentTransactionDto": { txId: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, gameId: { required: true, type: () => Number }, orderId: { required: true, type: () => String }, amount: { required: true, type: () => Number }, currency: { required: true, type: () => String }, paymentMethod: { required: true, enum: t["./constants/payment"].PaymentMethod }, status: { required: true, enum: t["./constants/payment"].PaymentStatus }, note: { required: true, type: () => String }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./modules/payment/payment-transaction.entity"), { "PaymentTransactionEntity": { txId: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, gameId: { required: true, type: () => Number }, orderId: { required: true, type: () => String }, amount: { required: true, type: () => Number }, currency: { required: true, type: () => String }, paymentMethod: { required: true, type: () => String }, status: { required: true, type: () => String }, note: { required: true, type: () => String }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, game: { required: true, type: () => String }, user: { required: true, type: () => String } } }], [import("./modules/user/dtos/create-user-profile.dto"), { "CreateUserProfileDto": { displayName: { required: false, type: () => String }, gender: { required: false, type: () => Boolean }, avatarUrl: { required: false, type: () => String }, dob: { required: false, type: () => String }, phone: { required: false, type: () => String }, address: { required: false, type: () => String } } }], [import("./common/dto/page-options.dto"), { "PageOptionsDto": { order: { required: true, enum: t["./constants/order"].Order }, page: { required: true, type: () => Number }, take: { required: true, type: () => Number }, q: { required: false, type: () => String } } }], [import("./common/dto/page-meta.dto"), { "PageMetaDto": { page: { required: true, type: () => Number }, take: { required: true, type: () => Number }, itemCount: { required: true, type: () => Number }, pageCount: { required: true, type: () => Number }, hasPreviousPage: { required: true, type: () => Boolean }, hasNextPage: { required: true, type: () => Boolean } } }], [import("./common/dto/page.dto"), { "PageDto": { data: { required: true }, meta: { required: true, type: () => t["./common/dto/page-meta.dto"].PageMetaDto } } }], [import("./common/dto/response.dto"), { "ResponseDto": { success: { required: true, type: () => Boolean }, message: { required: false, type: () => String, nullable: true }, data: { required: false, nullable: true } } }], [import("./modules/auth/dto/social-info.dto"), { "SocialInfoDto": { socialUid: { required: true, type: () => String }, name: { required: false, type: () => String, nullable: true }, email: { required: false, type: () => String, nullable: true }, avatarUrl: { required: false, type: () => String, nullable: true }, provider: { required: true, enum: t["./constants/user"].UserAccountType }, accessToken: { required: false, type: () => String, nullable: true }, refreshToken: { required: false, type: () => String, nullable: true } } }], [import("./modules/payment/dtos/transaction-history.dto"), { "TransactionHistoryDto": { txId: { required: false, type: () => Number }, gameId: { required: false, type: () => Number }, orderId: { required: false, type: () => String }, amount: { required: false, type: () => Number }, currency: { required: false, type: () => String }, paymentMethod: { required: false, enum: t["./constants/payment"].PaymentMethod }, status: { required: false, enum: t["./constants/payment"].PaymentStatus }, note: { required: false, type: () => String }, createdAt: { required: false, type: () => Date } } }], [import("./modules/payment/dtos/transaction-history-options.dto"), { "TransactionHistoryOptionsDto": { filterStatus: { required: false, enum: t["./constants/payment"].PaymentStatus }, dateFrom: { required: false, type: () => Date }, dateTo: { required: false, type: () => Date } } }], [import("./modules/user/dtos/update-user-profile.dto"), { "UpdateUserProfileDto": { displayName: { required: false, type: () => String, nullable: true }, gender: { required: false, type: () => Boolean, nullable: true }, avatarUrl: { required: false, type: () => String, nullable: true }, dob: { required: false, type: () => String, nullable: true }, phone: { required: false, type: () => String, nullable: true }, address: { required: false, type: () => String, nullable: true } } }], [import("./modules/user/dtos/user-quickplay-login.dto"), { "UserQuickplayLoginDto": { username: { required: false, type: () => String, nullable: true }, uniqueId: { required: true, type: () => String }, deviceId: { required: true, type: () => String }, platform: { required: true, enum: t["./constants/platform"].PlatformType } } }], [import("./modules/user/dtos/user-quickplay-link.dto"), { "UserQuickplayLinkDto": { password: { required: true, type: () => String }, qpId: { required: true, type: () => String }, qpToken: { required: true, type: () => String } } }], [import("./modules/user/dtos/user-quickplay-response.dto"), { "UserQuickplayResponseDto": { qpId: { required: true, type: () => String }, qpToken: { required: true, type: () => String }, createdAt: { required: true, type: () => Date } } }], [import("./modules/user/dtos/users-page-options.dto"), { "UsersPageOptionsDto": {} }], [import("./modules/admin/dtos/admin-account.dto"), { "AdminAccountDto": { adminId: { required: true, type: () => Number }, email: { required: false, type: () => String, nullable: true }, passwordHash: { required: true, type: () => String }, status: { required: false, enum: t["./modules/admin/dtos/admin-account.dto"].AdminAccountStatus }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, lastLoginAt: { required: true, type: () => Date } } }], [import("./modules/admin/admin-account.entity"), { "AdminAccountEntity": { adminId: { required: true, type: () => Number }, email: { required: false, type: () => String, nullable: true }, passwordHash: { required: true, type: () => String }, status: { required: true, type: () => String }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, lastLoginAt: { required: true, type: () => Date }, adminRoleMappings: { required: true, type: () => [String] }, auditLogs: { required: true, type: () => [String] } } }], [import("./modules/admin/dtos/admin-role-mapping.dto"), { "AdminRoleMappingDto": { armId: { required: false, type: () => String }, adminId: { required: false, type: () => String, nullable: true }, roleId: { required: false, type: () => Number }, gameId: { required: false, type: () => Number, nullable: true }, assignedAt: { required: false, type: () => Date }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./modules/admin/admin-role-mapping.entity"), { "AdminRoleMappingEntity": { armId: { required: true, type: () => String }, adminId: { required: true, type: () => String }, roleId: { required: true, type: () => Number }, gameId: { required: true, type: () => Number, nullable: true }, assignedAt: { required: true, type: () => Date }, admin: { required: true, type: () => String }, game: { required: true, type: () => String }, role: { required: true, type: () => String } } }], [import("./modules/admin/dtos/role.dto"), { "RoleDto": { roleName: { required: true, type: () => String }, description: { required: true, type: () => String } } }], [import("./modules/admin/role.entity"), { "RoleEntity": { roleId: { required: true, type: () => Number }, roleName: { required: true, type: () => String }, description: { required: true, type: () => String }, adminRoleMappings: { required: true, type: () => [String] } } }], [import("./modules/game/dtos/game.dto"), { "GameDto": { gameId: { required: true, type: () => Number }, gameKey: { required: true, type: () => String }, gameName: { required: true, type: () => String }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./modules/game/game.entity"), { "GameEntity": { gameId: { required: true, type: () => Number }, gameKey: { required: true, type: () => String }, gameName: { required: true, type: () => String }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date }, adminRoleMappings: { required: true, type: () => [String] }, paymentTransactions: { required: true, type: () => [String] }, userGameMappings: { required: true, type: () => [String] } } }], [import("./modules/auth/dto/user-register.dto"), { "UserRegisterDto": { username: { required: false, type: () => String, nullable: true }, password: { required: true, type: () => String } } }], [import("./modules/auth/dto/user-register-sso.dto"), { "UserRegisterSsoDto": { username: { required: false, type: () => String, nullable: true }, email: { required: false, type: () => String, nullable: true }, status: { required: true, enum: t["./constants/user"].UserAccountStatus }, accountType: { required: true, enum: t["./constants/user"].UserAccountType }, socialUid: { required: false, type: () => String, nullable: true }, linkedAt: { required: false, type: () => Date, nullable: true }, socialAccessToken: { required: false, type: () => String, nullable: true }, socialRefreshToken: { required: false, type: () => String, nullable: true }, createdAtIp: { required: false, type: () => String, nullable: true } } }], [import("./modules/auth/dto/token-payload.dto"), { "TokenPayloadDto": { userId: { required: true, type: () => Number }, expiresIn: { required: true, type: () => Number }, token: { required: true, type: () => String }, refreshToken: { required: true, type: () => String } } }], [import("./modules/auth/dto/user-login.dto"), { "UserLoginDto": { username: { required: false, type: () => String, nullable: true }, password: { required: true, type: () => String } } }], [import("./modules/auth/dto/login-payload.dto"), { "LoginPayloadDto": { user: { required: true, type: () => t["./modules/user/dtos/user-account.dto"].UserAccountDto }, accessToken: { required: true, type: () => t["./modules/auth/dto/token-payload.dto"].TokenPayloadDto } } }], [import("./modules/auth/dto/refresh-token.dto"), { "RefreshTokenDto": { refreshToken: { required: true, type: () => String } } }], [import("./modules/user/dtos/user.dto"), { "UserDto": { firstName: { required: false, type: () => String, nullable: true }, lastName: { required: false, type: () => String, nullable: true }, username: { required: true, type: () => String }, role: { required: false, enum: t["./constants/role-type"].RoleType }, email: { required: false, type: () => String, nullable: true }, avatar: { required: false, type: () => String, nullable: true }, phone: { required: false, type: () => String, nullable: true }, isActive: { required: false, type: () => Boolean } } }], [import("./modules/user/user-settings.entity"), { "UserSettingsEntity": { id: { required: true, type: () => Object }, isEmailVerified: { required: false, type: () => Boolean }, isPhoneVerified: { required: false, type: () => Boolean }, userId: { required: false, type: () => String }, user: { required: false, type: () => t["./modules/user/user.entity"].UserEntity } } }], [import("./modules/user/user.entity"), { "UserEntity": { id: { required: true, type: () => Object }, firstName: { required: true, type: () => String, nullable: true }, lastName: { required: true, type: () => String, nullable: true }, role: { required: true, enum: t["./constants/role-type"].RoleType }, email: { required: true, type: () => String, nullable: true }, password: { required: true, type: () => String, nullable: true }, phone: { required: true, type: () => String, nullable: true }, avatar: { required: true, type: () => String, nullable: true }, fullName: { required: true, type: () => String }, settings: { required: false, type: () => t["./modules/user/user-settings.entity"].UserSettingsEntity }, posts: { required: false, type: () => [t["./modules/post/post.entity"].PostEntity] } } }], [import("./modules/post/post-translation.entity"), { "PostTranslationEntity": { id: { required: true, type: () => Object }, title: { required: true, type: () => String }, description: { required: true, type: () => String }, postId: { required: true, type: () => Object }, post: { required: false, type: () => t["./modules/post/post.entity"].PostEntity } } }], [import("./modules/post/dtos/post-translation.dto"), { "PostTranslationDto": { title: { required: false, type: () => String }, description: { required: false, type: () => String }, languageCode: { required: false, enum: t["./constants/language-code"].LanguageCode } } }], [import("./modules/post/dtos/post.dto"), { "PostDto": { title: { required: false, type: () => String }, description: { required: false, type: () => String }, info: { required: true, type: () => String }, translations: { required: false, type: () => [t["./modules/post/dtos/post-translation.dto"].PostTranslationDto] } } }], [import("./modules/post/post.entity"), { "PostEntity": { id: { required: true, type: () => Object }, userId: { required: true, type: () => Object }, user: { required: true, type: () => t["./modules/user/user.entity"].UserEntity }, translations: { required: false, type: () => [t["./modules/post/post-translation.entity"].PostTranslationEntity] } } }], [import("./common/dto/create-translation.dto"), { "CreateTranslationDto": { languageCode: { required: true, enum: t["./constants/language-code"].LanguageCode }, text: { required: true, type: () => String } } }], [import("./modules/post/dtos/create-post.dto"), { "CreatePostDto": { title: { required: true, type: () => [t["./common/dto/create-translation.dto"].CreateTranslationDto] }, description: { required: true, type: () => [t["./common/dto/create-translation.dto"].CreateTranslationDto] } } }], [import("./modules/post/dtos/post-page-options.dto"), { "PostPageOptionsDto": {} }], [import("./modules/post/dtos/update-post.dto"), { "UpdatePostDto": {} }], [import("./modules/user/dtos/create-settings.dto"), { "CreateSettingsDto": { isEmailVerified: { required: false, type: () => Boolean }, isPhoneVerified: { required: false, type: () => Boolean } } }]], "controllers": [[import("./modules/user/user.controller"), { "UserController": { "getUserProfile": { type: t["./modules/user/dtos/user-account.dto"].UserAccountDto }, "updateUserProfile": {}, "createQuickplayUser": { type: t["./modules/user/dtos/user-quickplay-response.dto"].UserQuickplayResponseDto }, "linkUserQuickplay": {}, "getSocialInfo": { type: t["./modules/auth/dto/social-info.dto"].SocialInfoDto }, "unlinkSocialAccount": {}, "getTransactionHistory": {}, "admin": {}, "getUsers": {}, "getUser": { type: t["./modules/user/dtos/user-account.dto"].UserAccountDto } } }], [import("./modules/auth/auth.controller"), { "AuthController": { "userLogin": { type: t["./modules/auth/dto/login-payload.dto"].LoginPayloadDto }, "userRegister": { type: t["./modules/user/dtos/user-account.dto"].UserAccountDto }, "userRefreshToken": { type: t["./modules/auth/dto/token-payload.dto"].TokenPayloadDto }, "userLogout": {}, "googleLogin": {}, "googleCallback": {}, "facebookLogin": {}, "facebookCallback": {} } }], [import("./modules/health-checker/health-checker.controller"), { "HealthCheckerController": { "check": { type: Object } } }], [import("./modules/post/post.controller"), { "PostController": { "createPost": { type: t["./modules/post/dtos/post.dto"].PostDto }, "getPosts": {}, "getSinglePost": { type: t["./modules/post/dtos/post.dto"].PostDto }, "updatePost": {}, "deletePost": {} } }]] } };
};