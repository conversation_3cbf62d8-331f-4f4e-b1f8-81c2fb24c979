import { UserModule } from '@modules/user/user.module';
import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ApiConfigService } from '@shared/services/api-config.service';

import { AuthController } from './auth.controller.ts';
import { AuthService } from './auth.service.ts';
import { FacebookStrategy } from './facebook.strategy.ts';
import { GoogleStrategy } from './google.strategy.ts';
import { JwtStrategy } from './jwt.strategy.ts';
import { PublicStrategy } from './public.strategy.ts';

@Module({
	imports: [
		forwardRef(() => UserModule),
		PassportModule.register({ defaultStrategy: 'jwt' }),
		JwtModule.registerAsync({
			useFactory: (configService: ApiConfigService) => ({
				privateKey: configService.authConfig.privateKey,
				publicKey: configService.authConfig.publicKey,
				signOptions: {
					algorithm: 'RS256',
					expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				},
				verifyOptions: {
					algorithms: ['RS256'],
				},
				// if you want to use token with expiration date
				// signOptions: {
				//     expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				// },
			}),
			inject: [ApiConfigService],
		}),
		PassportModule,
	],
	controllers: [AuthController],
	providers: [
		AuthService,
		JwtStrategy,
		PublicStrategy,
		GoogleStrategy,
		FacebookStrategy,
	],
	exports: [JwtModule, AuthService],
})
export class AuthModule {}
